import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';

export const columns: BasicColumn[] = [
    {
    title: '标签名称',
    dataIndex: 'name',
    width: 150,
   },
   {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 150,
   },
   {
    title: '',
    dataIndex: '',
   }
];

export const searchFormSchema: FormSchema[] = [
 {
    label: '标签名称',
    field: 'name',
    component: 'Input'
  },
];

export const formSchema: FormSchema[] = [
  // TODO 主键隐藏字段，目前写死为ID
  {label: '', field: 'id', component: 'Input', show: false},
  {
    label: '标签名称',
    field: 'name',
    component: 'Input',
  },
];
