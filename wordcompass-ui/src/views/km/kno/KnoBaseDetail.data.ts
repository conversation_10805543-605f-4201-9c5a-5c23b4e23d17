import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { list as getCategoryList } from './KnoCategory.api';
export const columns: BasicColumn[] = [
  {
    title: '所属知识',
    dataIndex: 'knowledgeTitle'
   },  
  {
    title: '明细标题',
    dataIndex: 'title'
   },
   {
    title: '明细内容',
    dataIndex: 'content'
   },
   {
    title: '话术',
    dataIndex: 'srcipt'
   },
   {
    title: '话术简化',
    dataIndex: 'srciptSimplify'
   },
   {
    title: '所属类别',
    dataIndex: 'categoryName'
   },
   {
    title: '父类别ID，0表示一级类别',
    dataIndex: 'parentId',
    defaultHidden: true
   },

   {
    title: '关键词',
    dataIndex: 'keywords'
   },
   {
    title: '查看次数',
    dataIndex: 'viewCount'
   },
   {
    title: '状态',
    dataIndex: 'status',
    customRender: ({text}) => {
        return text === 0 ? '草稿' : text === 1 ? '发布' : '归档';
    } 
   },
   {
    title: '来源',
    dataIndex: 'sourceType',
    customRender: ({text}) => {
        return text === 1 ? '手动创建' : text === 2 ? '导入' : 'AI创建';
    }
   },
   {
    title: '创建者名称',
    dataIndex: 'creatorName'
   },
   {
    title: '最后更新者名称',
    dataIndex: 'updaterName'
   },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '所属知识',
    field: 'knowledgeTitle',
    component: 'Input',
    componentProps: {
      placeholder: '请选择所属知识',
      suffix: '',
      onclick: () => {
        // 触发选择弹窗
        const event = new Event('openSelectModal');
        window.dispatchEvent(event);
      },
    },
  },
  {
    label: '知识ID',
    field: 'knowledgeId',
    component: 'Input',
    show: false, // 隐藏此字段，仅用于查询使用
  },
  {
    label: '明细标题',
    field: 'title',
    component: 'Input'
  },
 {
    label: '明细内容',
    field: 'content',
    component: 'Input'
  },
];

export const formSchema: FormSchema[] = [
  // TODO 主键隐藏字段，目前写死为ID
  {label: '', field: 'id', component: 'Input', show: false},
  {
    label: '明细标题',
    field: 'title',
    component: 'Input',
    required: true,
  },
  {
    label: '明细内容',
    field: 'content',
    component: 'InputTextArea',
    componentProps: {
      rows: 10,
    },
    required: true,
  },
  {
    label: '话术',
    field: 'srcipt',
    component: 'InputTextArea',
    componentProps: {
      rows: 10,
    },
  },
  {
    label: '话术简化',
    field: 'srciptSimplify',
    component: 'InputTextArea',
    componentProps: {
      rows: 10,
    },
  },
  {
    label: '所属类别',
    field: 'categoryId',
    component: 'ApiSelect',
    componentProps: {
      api: getCategoryList,
      resultField: 'records',
      labelField: 'name',
      valueField: 'id',
      params: {
        status: 1,
      },
      allowClear: true,
      showSearch: true,
      filterOption: false,
      onChange: (value) => {
        console.log(value);
      },
      onSearch: (value) => {
        console.log(value);
      },
    },
    required: true,
  },
  {
    label: '父类别ID，0表示一级类别',
    field: 'parentId',
    component: 'Input',
    show: false,
  },
  {
    label: '关联的知识',
    field: 'knoBaseTitle',
    component: 'Input',
    show: false,
  },
  {
    label: '关联的知识id',
    field: 'knowledgeId',
    component: 'Input',
    show: false,
  },
  {
    label: '关键词(多个用逗号隔开)',
    field: 'keywords',
    component: 'Input',
  },
  {
    label: '查看次数',
    field: 'viewCount',
    component: 'InputNumber',
  },
  {
    label: '状态',
    field: 'status',
    component: 'Select',
    componentProps: {
        options: [
            {label: '草稿', value: 0},
            {label: '发布', value: 1},
            {label: '归档', value: 2},
        ]
    },
    required: true,
  },
  {
    label: '来源',
    field: 'sourceType',
    component: 'Select',
    componentProps: {
        options: [
            {label: '手动创建', value: 1},
            {label: '导入', value: 2},
            {label: 'AI创建', value: 3},
        ]
    },
    required: true,
  },
  {
    label: '创建者名称',
    field: 'creatorName',
    component: 'Input',
  },
  {
    label: '最后更新者名称',
    field: 'updaterName',
    component: 'Input',
  },
];
