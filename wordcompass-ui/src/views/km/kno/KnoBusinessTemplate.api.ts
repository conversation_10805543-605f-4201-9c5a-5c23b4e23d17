import { defHttp } from '/@/utils/http/axios';

const Api = {
  KnoBusinessTemplateList: `/kno/knoBusinessTemplate/list`,
  KnoBusinessTemplateAdd: `/kno/knoBusinessTemplate/add`,
  KnoBusinessTemplateEdit: `/kno/knoBusinessTemplate/edit`,
  KnoBusinessTemplateDelete: `/kno/knoBusinessTemplate/delete`,
  KnoBusinessTemplateDeleteBatch: `/kno/knoBusinessTemplate/deleteBatch`,
  KnoBusinessTemplateQueryById: `/kno/knoBusinessTemplate/queryById`,
  KnoBusinessTemplateCreateNewVersion: `/kno/knoBusinessTemplate/createNewVersion`,
  KnoBusinessTemplateGetKnoBaseIds: `/kno/knoBusinessTemplate/getKnoBaseIds`,
};

/**
 * 获取知识业务模板列表
 */
export const getKnoBusinessTemplateList = (params) =>
  defHttp.get({ url: Api.KnoBusinessTemplateList, params });

/**
 * 添加知识业务模板
 */
export const addKnoBusinessTemplate = (params: any) =>
  defHttp.post({ url: Api.KnoBusinessTemplateAdd, params });

/**
 * 编辑知识业务模板
 */
export const editKnoBusinessTemplate = (params: any) =>
  defHttp.post({ url: Api.KnoBusinessTemplateEdit, params });

/**
 * 删除知识业务模板
 */
export const deleteKnoBusinessTemplate = (params: any) =>
  defHttp.post({ url: Api.KnoBusinessTemplateDelete, params }, {joinParamsToUrl: true});

/**
 * 批量删除知识业务模板
 */
export const deleteBatchKnoBusinessTemplate = (params: any) =>
  defHttp.post({ url: Api.KnoBusinessTemplateDeleteBatch, params }, {joinParamsToUrl: true});

/**
 * 根据ID查询知识业务模板详情
 */
export const getKnoBusinessTemplateById = (params: any) =>
  defHttp.get({ url: Api.KnoBusinessTemplateQueryById, params });

/**
 * 创建新版本
 */
export const createNewVersion = (params: any) =>
  defHttp.post({ url: Api.KnoBusinessTemplateCreateNewVersion, params }, {joinParamsToUrl: true});

/**
 * 根据模板ID获取关联的知识库列表
 */
export const getKnoBaseIdsByTemplate = (params: any) =>
  defHttp.get({ url: Api.KnoBusinessTemplateGetKnoBaseIds, params }); 