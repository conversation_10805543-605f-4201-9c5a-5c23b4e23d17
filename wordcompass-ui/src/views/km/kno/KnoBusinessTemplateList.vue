<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" @click="handleCreate" preIcon="ant-design:plus-outlined">
          新增
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>
            批量操作
            <Icon icon="mdi:chevron-down"></Icon>
          </a-button>
        </a-dropdown>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction
          :actions="getTableAction(record)"
          :dropDownActions="getDropDownAction(record)"
        />
      </template>
    </BasicTable>
    
    <!-- 表单区域 -->
    <KnoBusinessTemplateModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" name="kno-knoBusinessTemplate" setup>
  import { ref } from 'vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useMessage } from '/@/hooks/web/useMessage';
  import KnoBusinessTemplateModal from './modules/KnoBusinessTemplateModal.vue';
  import { columns, searchFormSchema } from './KnoBusinessTemplate.data';
  import {
    getKnoBusinessTemplateList,
    deleteKnoBusinessTemplate,
    deleteBatchKnoBusinessTemplate,
    createNewVersion,
  } from './KnoBusinessTemplate.api';

  const { createMessage } = useMessage();
  const [registerModal, { openModal }] = useModal();

  // 列表页面公共参数、方法
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '知识业务模板',
      api: getKnoBusinessTemplateList,
      columns,
      canResize: false,
      formConfig: {
        labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
      },
      actionColumn: {
        width: 120,
        title: '操作',
        dataIndex: 'action',
        slots: { customRender: 'action' },
      },
      beforeFetch: (params) => {
        return Object.assign(params);
      },
    },
    exportConfig: {
      name: '知识业务模板列表',
      url: '/km/kno/knoBusinessTemplate/exportXls',
    },
    importConfig: {
      url: '/km/kno/knoBusinessTemplate/importExcel',
      success: handleSuccess,
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  /**
   * 新增事件
   */
  function handleCreate() {
    openModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }

  /**
   * 编辑事件
   */
  function handleEdit(record: any) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }

  /**
   * 详情
   */
  function handleDetail(record: any) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }

  /**
   * 删除事件
   */
  async function handleDelete(record: any) {
    try {
      await deleteKnoBusinessTemplate({ id: record.id });
      createMessage.success('删除成功');
      handleSuccess();
    } catch (error) {
      createMessage.error('删除失败');
    }
  }

  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    if (selectedRowKeys.value.length === 0) {
      createMessage.warning('请选择要删除的记录');
      return;
    }

    try {
      await deleteBatchKnoBusinessTemplate({ ids: selectedRowKeys.value });
      createMessage.success('批量删除成功');
      handleSuccess();
    } catch (error) {
      createMessage.error('批量删除失败');
    }
  }

  /**
   * 创建新版本
   */
  async function handleCreateNewVersion(record: any) {
    try {
      const result = await createNewVersion({ originId: record.id });
      if (result.success) {
        createMessage.success('创建新版本成功！');
        handleSuccess();
      }
    } catch (error) {
      createMessage.error('创建新版本失败！');
    }
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    selectedRowKeys.value = [];
    reload();
  }

  /**
   * 操作栏
   */
  function getTableAction(record: any) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        icon: 'clarity:note-edit-line',
      },
    ];
  }

  /**
   * 下拉操作栏
   */
  function getDropDownAction(record: any) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
        icon: 'ant-design:eye-outlined',
      },
      {
        label: '删除',
        color: 'error' as const,
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
        },
        icon: 'ant-design:delete-outlined',
      },
      {
        label: '创建新版本',
        onClick: handleCreateNewVersion.bind(null, record),
        icon: 'ant-design:copy-outlined',
      },
    ];
  }
</script>

<style scoped>
/* 可以添加自定义样式 */
</style> 