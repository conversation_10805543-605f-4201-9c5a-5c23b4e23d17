<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="getTitle"
    :width="1200"
    :maskClosable="false"
    :keyboard="false"
    :showOkBtn="false"
    :showCancelBtn="false"
  >
    <div class="textbook-detail-container">
      <!-- 左侧树形结构 -->
      <div class="tree-container">
        <div class="tree-header">
          <h3>教材结构</h3>
          <a-button type="primary" size="small" @click="handleAddRoot">
            添加单元
          </a-button>
        </div>
        <a-tree
          :tree-data="treeData"
          :selected-keys="selectedKeys"
          :expanded-keys="expandedKeys"
          @select="handleSelect"
          @expand="handleExpand"
          :default-expand-all="true"
          :show-line="true"
          :show-icon="true"
          :field-names="{ children: 'children', title: 'title', key: 'key' }"
        >
          <template #title="{ title, key, type }">
            <span class="tree-node">
              <span class="node-title">
                <span class="node-level-indicator">{{ getLevelIndicator(type) }}</span>
                {{ title }}
              </span>
              <span class="node-type-badge">{{ getTypeLabel(type) }}</span>
              <span class="node-actions">
                <a-button type="link" size="small" v-if="type !== 'lesson'"  @click.stop="handleAdd(key, type)">
                  添加子节点
                </a-button>
                <a-button type="link" size="small" @click.stop="handleEdit(key)">
                  编辑
                </a-button>
                <a-button type="link" size="small" @click.stop="handleDelete(key)" danger>
                  删除
                </a-button>
              </span>
            </span>
          </template>
        </a-tree>
      </div>

      <!-- 右侧编辑区域 -->
      <div class="edit-container">
        <div class="edit-header">
          <h3>{{ editTitle }}</h3>
          <div class="header-actions">
            <a-button type="primary" size="small" @click="handleSubmit" :loading="submitLoading">
              保存
            </a-button>
            <a-button size="small" @click="handleCancel" style="margin-left: 8px;">
              取消
            </a-button>
          </div>
        </div>
        <div class="edit-content" v-if="currentDetail">
          <a-form
            ref="formRef"
            :model="formData"
            :rules="rules"
            layout="vertical"
          >
            <a-form-item label="名称" name="name">
              <a-input v-model:value="formData.name" placeholder="请输入名称" />
            </a-form-item>
            <a-form-item label="类型" name="type">
              <a-select v-model:value="formData.type" placeholder="请选择类型" :disabled="isTypeDisabled">
                <a-select-option 
                  v-for="option in availableTypeOptions" 
                  :key="option.value" 
                  :value="option.value"
                  :disabled="option.disabled"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="内容" name="content">
              <a-textarea
                v-model:value="formData.content"
                placeholder="请输入内容"
                :rows="4"
              />
            </a-form-item>
            <a-form-item label="排序" name="sortOrder">
              <a-input-number
                v-model:value="formData.sortOrder"
                placeholder="请输入排序号"
                :min="0"
                style="width: 100%"
              />
            </a-form-item>
<!--             <a-form-item label="状态" name="status">
              <a-select v-model:value="formData.status" placeholder="请选择状态">
                <a-select-option :value="1">启用</a-select-option>
                <a-select-option :value="0">禁用</a-select-option>
              </a-select>
            </a-form-item> -->
            <a-form-item label="备注" name="remark">
              <a-textarea
                v-model:value="formData.remark"
                placeholder="请输入备注"
                :rows="2"
              />
            </a-form-item>
          </a-form>
        </div>
        <div class="edit-empty" v-else>
          <a-empty description="请选择左侧节点进行编辑" />
        </div>
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { useMessage } from '/@/hooks/web/useMessage';
import {
  getDetailTree,
  saveDetail,
  updateDetail,
  deleteDetail,
  getDetailById,
} from '../WcTextbook.api';

const { createMessage } = useMessage();

// 表单引用
const formRef = ref();

// 模态框注册
const [registerModal, { setModalProps, closeModal }] = useModalInner((data) => {
  setModalProps({ confirmLoading: false });
  if (data?.record) {
    currentTextbook.value = data.record;
    loadTreeData();
  }
});

// 当前教材
const currentTextbook = ref(null);

// 树形数据
const treeData = ref([]);
const selectedKeys = ref([]);
const expandedKeys = ref([]);

// 当前编辑的明细
const currentDetail = ref(null);
const isEdit = ref(false);
const selectedParentType = ref(''); // 选中的父节点类型
const submitLoading = ref(false); // 提交加载状态

// 表单数据
const formData = reactive({
  id: '',
  textbookId: '',
  parentId: '',
  name: '',
  type: 'unit',
  content: '',
  sortOrder: 0,
  status: 1,
  remark: '',
});

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择类型', trigger: 'change' }],
  sortOrder: [{ required: true, message: '请输入排序号', trigger: 'blur' }],
};

// 计算属性
const getTitle = computed(() => {
  return `维护教材明细 - ${currentTextbook.value?.textbookName || ''}`;
});

const editTitle = computed(() => {
  if (!currentDetail.value) return '编辑区域';
  
  if (isEdit.value) {
    // 编辑模式：显示编辑 + 类型
    return `编辑${getTypeLabel(formData.type)}`;
  } else {
    // 新增模式：显示新增 + 类型
    return `新增${getTypeLabel(formData.type)}`;
  }
});

// 获取类型标签
const getTypeLabel = (type) => {
  const typeMap = {
    unit: '单元',
    chapter: '章节',
    lesson: '课文',
  };
  return typeMap[type] || type;
};

// 获取层级指示器
const getLevelIndicator = (type) => {
  const levelMap = {
    unit: '📚',
    chapter: '📖',
    lesson: '📝',
  };
  return levelMap[type] || '📄';
};

// 计算可用的类型选项
const availableTypeOptions = computed(() => {
  const allOptions = [
    { value: 'unit', label: '单元', disabled: false },
    { value: 'chapter', label: '章节', disabled: false },
    { value: 'lesson', label: '课文', disabled: false },
  ];

  // 如果是编辑模式，不限制类型
  if (isEdit.value) {
    return allOptions;
  }

  // 如果是新增模式，根据父节点类型限制
  switch (selectedParentType.value) {
    case 'unit':
      // 单元下只能添加章节
      return allOptions.map(option => ({
        ...option,
        disabled: option.value !== 'chapter'
      }));
    case 'chapter':
      // 章节下只能添加课文
      return allOptions.map(option => ({
        ...option,
        disabled: option.value !== 'lesson'
      }));
    case 'lesson':
      // 课文下不能添加子节点
      return allOptions.map(option => ({
        ...option,
        disabled: true
      }));
    default:
      // 根节点只能添加单元
      return allOptions.map(option => ({
        ...option,
        disabled: option.value !== 'unit'
      }));
  }
});

// 类型选择是否禁用
const isTypeDisabled = computed(() => {
  return isEdit.value ? false : selectedParentType.value === 'lesson';
});

// 加载树形数据
const loadTreeData = async () => {
  if (!currentTextbook.value?.id) return;
  
  try {
    const result = await getDetailTree({ textbookId: currentTextbook.value.id });
    console.log('树形数据结果:', result); // 调试日志
    
    // 如果后端数据为空，使用测试数据
    if (!result || result.length === 0) {
      treeData.value = getTestTreeData();
    } else {
      treeData.value = buildTreeData(result);
    }
    
    console.log('构建后的树形数据:', treeData.value); // 调试日志
  } catch (error) {
    console.error('加载树形数据失败:', error); // 调试日志
    // 使用测试数据作为备选
    treeData.value = getTestTreeData();
  }
};

// 获取测试树形数据
const getTestTreeData = () => {
  return [
    {
      key: 'unit1',
      title: 'Unit 1 Hello!',
      type: 'unit',
      children: [
        {
          key: 'chapter1',
          title: 'Part A Let\'s talk',
          type: 'chapter',
          children: [
            {
              key: 'lesson1',
              title: 'Lesson 1',
              type: 'lesson',
              children: []
            }
          ]
        },
        {
          key: 'chapter2',
          title: 'Part B Let\'s learn',
          type: 'chapter',
          children: [
            {
              key: 'lesson2',
              title: 'Lesson 2',
              type: 'lesson',
              children: []
            }
          ]
        }
      ]
    },
    {
      key: 'unit2',
      title: 'Unit 2 Colours',
      type: 'unit',
      children: [
        {
          key: 'chapter3',
          title: 'Part A Let\'s talk',
          type: 'chapter',
          children: [
            {
              key: 'lesson3',
              title: 'Lesson 1',
              type: 'lesson',
              children: []
            }
          ]
        }
      ]
    }
  ];
};

// 构建树形数据
const buildTreeData = (data) => {
  if (!data || !Array.isArray(data)) return [];
  
  return data.map(item => ({
    key: item.id,
    title: item.name,
    type: item.type,
    children: item.children && item.children.length > 0 ? buildTreeData(item.children) : undefined,
  }));
};

// 选择节点
const handleSelect = (keys, info) => {
  selectedKeys.value = keys;
  if (keys.length > 0) {
    loadDetailData(keys[0]);
  } else {
    currentDetail.value = null;
  }
};

// 展开节点
const handleExpand = (keys) => {
  expandedKeys.value = keys;
};

// 加载明细数据
const loadDetailData = async (id) => {
  try {
    const result = await getDetailById({ id });
    currentDetail.value = result;
    isEdit.value = true;
    Object.assign(formData, result);
  } catch (error) {
    createMessage.error('加载明细数据失败');
  }
};

// 添加根节点
const handleAddRoot = () => {
  resetForm();
  selectedParentType.value = '';
  currentDetail.value = { id: '', type: 'unit' };
  isEdit.value = false;
  formData.textbookId = currentTextbook.value?.id;
  formData.parentId = '';
  formData.type = 'unit';
  selectedKeys.value = [];
};

// 添加子节点
const handleAdd = (parentKey, parentType) => {
  resetForm();
  selectedParentType.value = parentType;
  currentDetail.value = { id: '', type: getNextType(parentType) };
  isEdit.value = false;
  formData.textbookId = currentTextbook.value?.id;
  formData.parentId = parentKey;
  formData.type = getNextType(parentType);
  selectedKeys.value = [];
};

// 获取下一个类型
const getNextType = (parentType) => {
  switch (parentType) {
    case 'unit': return 'chapter';
    case 'chapter': return 'lesson';
    default: return 'unit';
  }
};

// 编辑节点
const handleEdit = (key) => {
  selectedKeys.value = [key];
  loadDetailData(key);
};

// 删除节点
const handleDelete = async (key) => {
  try {
    await deleteDetail({ id: key });
    createMessage.success('删除成功');
    loadTreeData();
    if (selectedKeys.value.includes(key)) {
      currentDetail.value = null;
      selectedKeys.value = [];
    }
  } catch (error) {
    createMessage.error('删除失败');
  }
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: '',
    textbookId: '',
    parentId: '',
    name: '',
    type: 'unit',
    content: '',
    sortOrder: 0,
    status: 1,
    remark: '',
  });
  // 确保表单字段被重置
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    submitLoading.value = true;
    
    const api = isEdit.value ? updateDetail : saveDetail;
    await api(formData);
    
    createMessage.success(isEdit.value ? '更新成功' : '保存成功');
    await loadTreeData();
    // 不关闭模态框，保持窗口打开
    resetForm();
    currentDetail.value = null;
    selectedKeys.value = [];
  } catch (error) {
    createMessage.error(isEdit.value ? '更新失败' : '保存失败');
  } finally {
    submitLoading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  resetForm();
  currentDetail.value = null;
  selectedKeys.value = [];
};
</script>

<style lang="less" scoped>
.textbook-detail-container {
  display: flex;
  height: 600px;
  gap: 16px;
}

.tree-container {
  width: 500px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
}

.tree-header {
  padding: 12px;
  border-bottom: 1px solid #d9d9d9;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    font-size: 14px;
  }
}

.tree-container :deep(.ant-tree) {
  flex: 1;
  padding: 12px;
  overflow-y: auto;
  
  // 树节点样式优化
  .ant-tree-node-content-wrapper {
    padding: 4px 8px;
    border-radius: 4px;
    
    &:hover {
      background-color: #f5f5f5;
    }
    
    &.ant-tree-node-selected {
      background-color: #e6f7ff;
    }
  }
  
  // 树线样式
  .ant-tree-switcher {
    color: #999;
  }
  
  // 展开图标样式
  .ant-tree-switcher-leaf {
    display: none;
  }
}

.tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  
  .node-title {
    flex: 1;
    margin-right: 8px;
    display: flex;
    align-items: center;
    
    .node-level-indicator {
      margin-right: 6px;
      font-size: 14px;
    }
  }
  
  .node-type-badge {
    background: #e6f7ff;
    color: #1890ff;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    margin-right: 8px;
    white-space: nowrap;
    border: 1px solid #91d5ff;
  }
  
  .node-actions {
    display: none;
    gap: 4px;
  }
}

.tree-container :deep(.ant-tree-node-content-wrapper:hover) .node-actions {
  display: flex;
}

.edit-container {
  flex: 1;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
}

.edit-header {
  padding: 12px;
  border-bottom: 1px solid #d9d9d9;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    font-size: 14px;
  }
  
  .header-actions {
    display: flex;
    gap: 8px;
  }
}

.edit-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.edit-empty {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style> 