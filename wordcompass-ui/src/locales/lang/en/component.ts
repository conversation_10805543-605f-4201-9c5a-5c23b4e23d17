export default {
  app: {
    searchNotData: 'No search results yet',
    toSearch: 'to search',
    toNavigate: 'to navigate',
  },
  countdown: {
    normalText: 'Get SMS code',
    sendText: 'Reacquire in {0}s',
  },
  cropper: {
    selectImage: 'Select Image',
    uploadSuccess: 'Uploaded success!',
    modalTitle: 'Avatar upload',
    okText: 'Confirm and upload',
    btn_reset: 'Reset',
    btn_rotate_left: 'Counterclockwise rotation',
    btn_rotate_right: 'Clockwise rotation',
    btn_scale_x: 'Flip horizontal',
    btn_scale_y: 'Flip vertical',
    btn_zoom_in: 'Zoom in',
    btn_zoom_out: 'Zoom out',
    preview: 'Preivew',
  },
  drawer: {
    loadingText: 'Loading...',
    cancelText: 'Close',
    okText: 'Confirm',
  },
  excel: {
    exportModalTitle: 'Export data',
    fileType: 'File type',
    fileName: 'File name',
  },
  form: {
    putAway: 'Put away',
    unfold: 'Unfold',
    maxTip: 'The number of characters should be less than {0}',
    apiSelectNotFound: 'Wait for data loading to complete...',
  },
  icon: {
    placeholder: 'Click the select icon',
    search: 'Search icon',
    copy: 'Copy icon successfully!',
  },
  menu: {
    search: 'Menu search',
  },
  modal: {
    cancelText: 'Close',
    okText: 'Confirm',
    close: 'Close',
    maximize: 'Maximize',
    restore: 'Restore',
  },
  table: {
    settingDens: 'Density',
    settingDensDefault: 'Default',
    settingDensMiddle: 'Middle',
    settingDensSmall: 'Compact',
    settingColumn: 'Column settings',
    settingColumnShow: 'Column display',
    settingIndexColumnShow: 'Index Column',
    settingSelectColumnShow: 'Selection Column',
    settingFixedLeft: 'Fixed Left',
    settingFixedRight: 'Fixed Right',
    settingFullScreen: 'Full Screen',
    index: 'Index',
    total: 'total of {total}',
  },
  time: {
    before: ' ago',
    after: ' after',
    just: 'just now',
    seconds: ' seconds',
    minutes: ' minutes',
    hours: ' hours',
    days: ' days',
  },
  tree: {
    selectAll: 'Select All',
    unSelectAll: 'Cancel Select',
    expandAll: 'Expand All',
    unExpandAll: 'Collapse all',

    checkStrictly: 'Hierarchical association',
    checkUnStrictly: 'Hierarchical independence',
  },
  upload: {
    save: 'Save',
    upload: 'Upload',
    imgUpload: 'ImageUpload',
    uploaded: 'Uploaded',

    operating: 'Operating',
    del: 'Delete',
    download: 'download',
    saveWarn: 'Please wait for the file to upload and save!',
    saveError: 'There is no file successfully uploaded and cannot be saved!',

    preview: 'Preview',
    choose: 'Select the file',

    accept: 'Support {0} format',
    acceptUpload: 'Only upload files in {0} format',
    maxSize: 'A single file does not exceed {0}MB ',
    maxSizeMultiple: 'Only upload files up to {0}MB!',
    maxNumber: 'Only upload up to {0} files',

    legend: 'Legend',
    fileName: 'File name',
    fileSize: 'File size',
    fileStatue: 'File status',

    startUpload: 'Start upload',
    uploadSuccess: 'Upload successfully',
    uploadError: 'Upload failed',
    uploading: 'Uploading',
    uploadWait: 'Please wait for the file upload to finish',
    reUploadFailed: 'Re-upload failed files',
  },
  verify: {
    error: 'verification failed！',
    time: 'The verification is successful and it takes {time} seconds！',
    redoTip: 'Click the picture to refresh',
    dragText: 'Hold down the slider and drag',
    successText: 'Verified',
  },
};
