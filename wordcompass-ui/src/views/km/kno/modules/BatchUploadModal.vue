<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="批量导入PPT文件"
    width="600px"
    @ok="handleSubmit"
  >
    <div class="batch-upload-container">
      <div class="upload-tips">
        <p>请选择要上传的PPT文件（支持.pptx格式）</p>
        <p>系统将自动解析PPT内容并生成知识基础信息</p>
      </div>
      
      <BasicForm @register="registerForm" />
    </div>
  </BasicModal>
</template>

<script setup>
import { ref } from 'vue'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { UploadTypeEnum } from '/@/components/Form/src/jeecg/components/JUpload'
import { batchUploadInit } from '../KnoBase.api'
import { useMessage } from '/@/hooks/web/useMessage'

const { createMessage } = useMessage()

const emit = defineEmits(['success', 'register'])

// 注册表单
const [registerForm, { getFieldsValue, resetFields, setFieldsValue }] = useForm({
  labelWidth: 120,
  schemas: [
    {
      field: 'uploadFiles',
      component: 'JUpload',
      label: '选择PPT文件',
      componentProps: {
        text: '选择PPT文件',
        maxCount: 10,
        fileType: UploadTypeEnum.file,
        bizPath: 'batchInitppt',
        returnUrl: false,
        multiple: true,
        download: false,
        removeConfirm: true,
        accept: '.ppt,.pptx',
        showUploadList: true,
        showPreviewIcon: false,
        showRemoveIcon: true,
      },
      colProps: { span: 24 },
    },
  ],
  showActionButtonGroup: false,
  showResetButton: false,
  showSubmitButton: false,
  showAdvancedButton: false,
})

const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  console.log('打开批量上传弹窗，data:', data)
  
  // 重置表单，清除上次上传的附件记录
  await resetFields();
  
  // 确保uploadFiles字段为空数组
  await setFieldsValue({
    uploadFiles: [],
  });
  
  console.log('已清除上次上传的附件记录')
})

// 提交处理
const handleSubmit = async () => {
  const formData = getFieldsValue()
  let uploadFiles = formData.uploadFiles

  console.log('原始uploadFiles数据:', uploadFiles)
  console.log('uploadFiles类型:', typeof uploadFiles)

  // 处理不同的数据格式
  if (!uploadFiles) {
    createMessage.warning('请先选择要上传的文件')
    return
  }

  // 如果是字符串，尝试解析为JSON
  if (typeof uploadFiles === 'string') {
    try {
      uploadFiles = JSON.parse(uploadFiles)
    } catch (e) {
      console.error('解析uploadFiles字符串失败:', e)
      createMessage.error('文件数据格式错误')
      return
    }
  }

  // 确保是数组
  if (!Array.isArray(uploadFiles)) {
    console.error('uploadFiles不是数组:', uploadFiles)
    createMessage.error('文件数据格式错误，请重新选择文件')
    return
  }

  if (uploadFiles.length === 0) {
    createMessage.warning('请先选择要上传的文件')
    return
  }

  try {
    setModalProps({ confirmLoading: true })
    
    // 准备附件数据
    const attachmentList = uploadFiles.map(file => {
      console.log('处理文件:', file)
      
      let filePath = file.filePath || file.url || file.response?.data || file.response?.url
      
      if (filePath && filePath.startsWith('http')) {
        const urlParts = filePath.split('/')
        filePath = urlParts.slice(-2).join('/')
      }
      
      if (!filePath) {
        filePath = file.fileName || file.name
      }
      
      return {
        fileName: file.fileName || file.name,
        filePath: filePath,
        fileSize: file.fileSize || file.size || 0,
        fileType: (file.fileName || file.name).split('.').pop().toLowerCase()
      }
    })

    console.log('准备发送的附件数据:', attachmentList)

    // 调用批量上传API
    await batchUploadInit(attachmentList)
    
    createMessage.success('批量上传初始化成功')
    emit('success')
    closeModal()
  } catch (error) {
    console.error('批量上传失败:', error)
    createMessage.error('批量上传失败：' + (error?.message || '未知错误'))
  } finally {
    setModalProps({ confirmLoading: false })
  }
}
</script>

<style scoped>
.batch-upload-container {
  padding: 20px;
}

.upload-tips {
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
}

.upload-tips p {
  margin: 5px 0;
  color: #666;
}
</style> 