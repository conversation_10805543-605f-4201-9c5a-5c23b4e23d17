import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { list as getKnoBaseList } from './KnoBase.api';
export const columns: BasicColumn[] = [
    {
    title: '知识标题',
    dataIndex: 'knowledgeTitle',
    customRender: ({ text, record }) => {
      return record.knowledgeTitle;
    }
   },
   {
    title: '访问用户',
    dataIndex: 'userId'
   },
   {
    title: 'IP地址',
    dataIndex: 'ipAddress'
   },
   {
    title: '用户代理',
    dataIndex: 'userAgent'
   },
   {
    title: '访问时间',
    dataIndex: 'accessTime'
   },
];

export const searchFormSchema: FormSchema[] = [
 {
    label: '知识标题',
    field: 'knowledgeId',
    component: 'ApiSelect',
    componentProps: {
      api: getKnoBaseList,
      params: {
        status: '1',
      },
      pageConfig: {
        isPage: true,
        pageNo: 1,
        pageSize: 2,
        totalField: 'total',
        listField: 'records'
      },
      showSearch: true,
      resultField: 'records',
      searchField: 'title',
      async: true,
      immediate: false,
      labelField: 'title',
      valueField: 'id',
    },
  },
 {
    label: '访问用户',
    field: 'userId',
    component: 'Input'
  },
];

export const formSchema: FormSchema[] = [
  // TODO 主键隐藏字段，目前写死为ID
  {label: '', field: 'id', component: 'Input', show: false},
  {
    label: '知识',
    field: 'knowledgeTitle',
    component: 'Input',
  },
  {
    label: '访问用户',
    field: 'userId',
    component: 'Input',
  },
  {
    label: 'IP地址',
    field: 'ipAddress',
    component: 'Input',
  },
  {
    label: '用户代理',
    field: 'userAgent',
    component: 'Input',
  },
  {
    label: '访问时间',
    field: 'accessTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD hh:mm:ss',
    },
  },
];
