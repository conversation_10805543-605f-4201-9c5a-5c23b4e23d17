<template>
  <div class="textbook-detail-page">
          <!-- 教材选择区域 -->
      <div class="search-section">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch" class="search-form">
          <a-form-item label="教材名称" name="textbookName">
            <a-input
              v-model:value="searchForm.textbookName"
              placeholder="请输入教材名称"
              style="width: 140px"
              allowClear
            />
          </a-form-item>
          <a-form-item label="年级" name="grade">
            <a-select
              v-model:value="searchForm.grade"
              placeholder="请选择年级"
              style="width: 120px"
              allowClear
            >
              <a-select-option value="一年级">一年级</a-select-option>
              <a-select-option value="二年级">二年级</a-select-option>
              <a-select-option value="三年级">三年级</a-select-option>
              <a-select-option value="四年级">四年级</a-select-option>
              <a-select-option value="五年级">五年级</a-select-option>
              <a-select-option value="六年级">六年级</a-select-option>
              <a-select-option value="七年级">七年级</a-select-option>
              <a-select-option value="八年级">八年级</a-select-option>
              <a-select-option value="九年级">九年级</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="版本" name="version">
            <a-input
              v-model:value="searchForm.version"
              placeholder="请输入版本"
              style="width: 120px"
              allowClear
            />
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit" preIcon="ant-design:search-outlined">
                查询
              </a-button>
              <a-button @click="handleReset" preIcon="ant-design:reload-outlined">
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧教材列表 -->
      <div class="left-panel">
        <div class="panel-header">
          <h3>教材列表</h3>
          <span class="textbook-count">{{ textbookList.length }} 本教材</span>
        </div>
        <div class="textbook-list">
          <a-list
            :data-source="textbookList"
            :loading="textbookLoading"
            item-layout="horizontal"
          >
            <template #renderItem="{ item }">
              <a-list-item
                :class="{ 'selected': selectedTextbook?.id === item.id }"
                @click="handleSelectTextbook(item)"
              >
                <a-list-item-meta>
                  <template #title>
                    <span class="textbook-title">{{ item.textbookName }}</span>
                  </template>
                  <template #description>
                    <div class="textbook-info">
                      <span class="grade-badge">{{ item.grade }}</span>
                      <span class="version-badge">{{ item.version }}</span>
                      <span class="publisher">{{ item.publisher }}</span>
                    </div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </div>

      <!-- 右侧教材明细管理 -->
      <div class="right-panel">
        <div class="panel-header">
          <h3>{{ rightPanelTitle }}</h3>
          <div class="header-actions" v-if="selectedTextbook">
            <a-button type="primary" size="small" @click="handleAddRoot">
              添加单元
            </a-button>
          </div>
        </div>
        
        <div class="detail-content" v-if="selectedTextbook">
          <!-- 树形结构展示 -->
          <div class="tree-section">
            <a-tree
              :tree-data="treeData"
              :selected-keys="selectedKeys"
              :expanded-keys="expandedKeys"
              @select="handleSelectNode"
              @expand="handleExpandNode"
              :default-expand-all="true"
              :show-line="true"
              :show-icon="true"
            >
              <template #title="{ title, key, type }">
                <span class="tree-node">
                  <span class="node-title">
                    <span class="node-level-indicator">{{ getLevelIndicator(type) }}</span>
                    {{ title }}
                  </span>
                  <span class="node-type-badge">{{ getTypeLabel(type) }}</span>
                  <span class="node-actions">
                    <a-button type="link" size="small" v-if="type !== 'lesson'" @click.stop="handleAddChild(key, type)">
                      添加子节点
                    </a-button>
                    <a-button type="link" size="small" @click.stop="handleEditNode(key)">
                      编辑
                    </a-button>
                    <a-button type="link" size="small" @click.stop="handleDeleteNode(key)" danger>
                      删除
                    </a-button>
                  </span>
                </span>
              </template>
            </a-tree>
          </div>
        </div>
        
        <div class="empty-content" v-else>
          <a-empty description="请选择左侧教材进行管理" />
        </div>
      </div>
    </div>

    <!-- 教材明细编辑模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :confirm-loading="modalLoading"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      width="600px"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-form-item label="名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入名称" />
        </a-form-item>
        <a-form-item label="类型" name="type">
          <a-select v-model:value="formData.type" placeholder="请选择类型" :disabled="isTypeDisabled">
            <a-select-option 
              v-for="option in availableTypeOptions" 
              :key="option.value" 
              :value="option.value"
              :disabled="option.disabled"
            >
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="内容" name="content">
          <a-textarea
            v-model:value="formData.content"
            placeholder="请输入内容"
            :rows="4"
          />
        </a-form-item>
        <a-form-item label="排序" name="sortOrder">
          <a-input-number
            v-model:value="formData.sortOrder"
            placeholder="请输入排序号"
            :min="0"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-select v-model:value="formData.status" placeholder="请选择状态">
            <a-select-option :value="1">启用</a-select-option>
            <a-select-option :value="0">禁用</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea
            v-model:value="formData.remark"
            placeholder="请输入备注"
            :rows="2"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useMessage } from '/@/hooks/web/useMessage';
import {
  getTextbookList,
  getDetailTree,
  saveDetail,
  updateDetail,
  deleteDetail,
  getDetailById,
} from './WcTextbookDetail.api';

const { createMessage } = useMessage();

// 搜索表单
const searchForm = reactive({
  grade: '',
  version: '',
  textbookName: '',
});

// 教材列表
const textbookList = ref([]);
const textbookLoading = ref(false);
const selectedTextbook = ref(null);

// 树形数据
const treeData = ref([]);
const selectedKeys = ref([]);
const expandedKeys = ref([]);

// 模态框
const modalVisible = ref(false);
const modalLoading = ref(false);
const modalTitle = ref('');
const isEdit = ref(false);
const selectedParentType = ref(''); // 选中的父节点类型

// 表单
const formRef = ref();
const formData = reactive({
  id: '',
  textbookId: '',
  parentId: '',
  name: '',
  type: 'unit',
  content: '',
  sortOrder: 0,
  status: 1,
  remark: '',
});

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择类型', trigger: 'change' }],
  sortOrder: [{ required: true, message: '请输入排序号', trigger: 'blur' }],
};

// 计算属性
const rightPanelTitle = computed(() => {
  if (!selectedTextbook.value) return '教材明细管理';
  return `教材明细管理 - ${selectedTextbook.value.textbookName}`;
});

// 获取类型标签
const getTypeLabel = (type) => {
  const typeMap = {
    unit: '单元',
    chapter: '章节',
    lesson: '课文',
  };
  return typeMap[type] || type;
};

// 获取层级指示器
const getLevelIndicator = (type) => {
  const levelMap = {
    unit: '📚',
    chapter: '📖',
    lesson: '📝',
  };
  return levelMap[type] || '📄';
};

// 计算可用的类型选项
const availableTypeOptions = computed(() => {
  const allOptions = [
    { value: 'unit', label: '单元', disabled: false },
    { value: 'chapter', label: '章节', disabled: false },
    { value: 'lesson', label: '课文', disabled: false },
  ];

  // 如果是编辑模式，不限制类型
  if (isEdit.value) {
    return allOptions;
  }

  // 如果是新增模式，根据父节点类型限制
  switch (selectedParentType.value) {
    case 'unit':
      // 单元下只能添加章节
      return allOptions.map(option => ({
        ...option,
        disabled: option.value !== 'chapter'
      }));
    case 'chapter':
      // 章节下只能添加课文
      return allOptions.map(option => ({
        ...option,
        disabled: option.value !== 'lesson'
      }));
    case 'lesson':
      // 课文下不能添加子节点
      return allOptions.map(option => ({
        ...option,
        disabled: true
      }));
    default:
      // 根节点只能添加单元
      return allOptions.map(option => ({
        ...option,
        disabled: option.value !== 'unit'
      }));
  }
});

// 类型选择是否禁用
const isTypeDisabled = computed(() => {
  return isEdit.value ? false : selectedParentType.value === 'lesson';
});

// 初始化
onMounted(() => {
  loadTextbookList();
});

// 加载教材列表
const loadTextbookList = async () => {
  textbookLoading.value = true;
  try {
    const result = await getTextbookList(searchForm);
    textbookList.value = result.records || result;
  } catch (error) {
    createMessage.error('加载教材列表失败');
  } finally {
    textbookLoading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  loadTextbookList();
};

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    grade: '',
    version: '',
    textbookName: '',
  });
  loadTextbookList();
};

// 选择教材
const handleSelectTextbook = async (textbook) => {
  selectedTextbook.value = textbook;
  selectedKeys.value = [];
  await loadTreeData();
};

// 加载树形数据
const loadTreeData = async () => {
  if (!selectedTextbook.value?.id) return;
  
  try {
    const result = await getDetailTree({ textbookId: selectedTextbook.value.id });
    treeData.value = buildTreeData(result);
  } catch (error) {
    createMessage.error('加载树形数据失败');
  }
};

// 构建树形数据
const buildTreeData = (data) => {
  return data.map(item => ({
    key: item.id,
    title: item.name,
    type: item.type,
    children: item.children ? buildTreeData(item.children) : [],
  }));
};

// 选择节点
const handleSelectNode = (keys) => {
  selectedKeys.value = keys;
};

// 展开节点
const handleExpandNode = (keys) => {
  expandedKeys.value = keys;
};

// 添加根节点
const handleAddRoot = () => {
  resetForm();
  selectedParentType.value = '';
  modalTitle.value = '添加单元';
  isEdit.value = false;
  formData.textbookId = selectedTextbook.value?.id;
  formData.parentId = '';
  formData.type = 'unit';
  modalVisible.value = true;
};

// 添加子节点
const handleAddChild = (parentKey, parentType) => {
  resetForm();
  selectedParentType.value = parentType;
  modalTitle.value = '添加子节点';
  isEdit.value = false;
  formData.textbookId = selectedTextbook.value?.id;
  formData.parentId = parentKey;
  formData.type = getNextType(parentType);
  modalVisible.value = true;
};

// 获取下一个类型
const getNextType = (parentType) => {
  switch (parentType) {
    case 'unit': return 'chapter';
    case 'chapter': return 'lesson';
    default: return 'unit';
  }
};

// 编辑节点
const handleEditNode = async (key) => {
  try {
    const result = await getDetailById({ id: key });
    Object.assign(formData, result);
    modalTitle.value = '编辑节点';
    isEdit.value = true;
    modalVisible.value = true;
  } catch (error) {
    createMessage.error('加载节点数据失败');
  }
};

// 删除节点
const handleDeleteNode = async (key) => {
  try {
    await deleteDetail({ id: key });
    createMessage.success('删除成功');
    await loadTreeData();
    if (selectedKeys.value.includes(key)) {
      selectedKeys.value = [];
    }
  } catch (error) {
    createMessage.error('删除失败');
  }
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: '',
    textbookId: '',
    parentId: '',
    name: '',
    type: 'unit',
    content: '',
    sortOrder: 0,
    status: 1,
    remark: '',
  });
  formRef.value?.resetFields();
};

// 模态框确认
const handleModalOk = async () => {
  try {
    await formRef.value?.validate();
    modalLoading.value = true;
    
    const api = isEdit.value ? updateDetail : saveDetail;
    await api(formData);
    
    createMessage.success(isEdit.value ? '更新成功' : '保存成功');
    await loadTreeData();
    modalVisible.value = false;
  } catch (error) {
    createMessage.error(isEdit.value ? '更新失败' : '保存失败');
  } finally {
    modalLoading.value = false;
  }
};

// 模态框取消
const handleModalCancel = () => {
  modalVisible.value = false;
  resetForm();
};
</script>

<style lang="less" scoped>
.textbook-detail-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
}

// 搜索区域
.search-section {
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
  
  .search-form {
    :deep(.ant-form) {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      gap: 16px;
    }
    
    :deep(.ant-form-item) {
      margin-bottom: 0;
      margin-right: 0;
      flex-shrink: 0;
      
      .ant-form-item-label {
        label {
          color: #262626;
          font-weight: 500;
          white-space: nowrap;
        }
      }
      
      .ant-input, .ant-select-selector {
        border-radius: 4px;
        border: 1px solid #d9d9d9;
        
        &:hover {
          border-color: #40a9ff;
        }
        
        &:focus {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }
      
      .ant-btn {
        border-radius: 4px;
        font-weight: 500;
        
        &.ant-btn-primary {
          background: #1890ff;
          border-color: #1890ff;
          
          &:hover {
            background: #40a9ff;
            border-color: #40a9ff;
          }
        }
        
        &.ant-btn-default {
          border-color: #d9d9d9;
          color: #595959;
          
          &:hover {
            border-color: #40a9ff;
            color: #40a9ff;
          }
        }
      }
    }
  }
}

// 主要内容区域
.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

// 左侧面板
.left-panel {
  width: 300px;
  border-right: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
  background: #fff;
  
  .panel-header {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #262626;
    }
    
    .textbook-count {
      background: #f5f5f5;
      padding: 2px 8px;
      border-radius: 10px;
      font-size: 12px;
      color: #666;
    }
  }
  
  .textbook-list {
    flex: 1;
    overflow-y: auto;
    
    :deep(.ant-list-item) {
      cursor: pointer;
      padding: 12px 16px;
      border-bottom: 1px solid #f5f5f5;
      
      &:hover {
        background-color: #f5f5f5;
      }
      
      &.selected {
        background-color: #e6f7ff;
        border-right: 3px solid #1890ff;
      }
      
      &:last-child {
        border-bottom: none;
      }
    }
    
    .textbook-title {
      font-weight: 500;
      color: #262626;
      margin-bottom: 4px;
    }
    
    .textbook-info {
      display: flex;
      gap: 8px;
      align-items: center;
      
      .grade-badge, .version-badge {
        background: #f0f0f0;
        color: #666;
        padding: 1px 6px;
        border-radius: 8px;
        font-size: 11px;
        font-weight: 500;
      }
      
      .publisher {
        color: #8c8c8c;
        font-size: 12px;
      }
    }
  }
}

// 右侧面板
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #fff;
  
  .panel-header {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #262626;
    }
  }
  
  .detail-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    
    .tree-section {
      :deep(.ant-tree) {
        .ant-tree-node-content-wrapper {
          padding: 4px 8px;
          border-radius: 4px;
          
          &:hover {
            background-color: #f5f5f5;
          }
          
          &.ant-tree-node-selected {
            background-color: #e6f7ff;
          }
        }
        
        .ant-tree-switcher {
          color: #999;
        }
        
        .ant-tree-switcher-leaf {
          display: none;
        }
      }
      
      .tree-node {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        
        .node-title {
          flex: 1;
          margin-right: 8px;
          display: flex;
          align-items: center;
          gap: 6px;
          
          .node-level-indicator {
            font-size: 14px;
          }
        }
        
        .node-type-badge {
          background: #f0f0f0;
          color: #666;
          padding: 2px 6px;
          border-radius: 3px;
          font-size: 11px;
          margin-right: 8px;
        }
        
        .node-actions {
          display: none;
          gap: 4px;
        }
      }
      
      :deep(.ant-tree-node-content-wrapper:hover) .node-actions {
        display: flex;
      }
    }
  }
  
  .empty-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }

}
</style> 