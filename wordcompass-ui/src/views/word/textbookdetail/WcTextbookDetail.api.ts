import { defHttp } from '/@/utils/http/axios';

enum Api {
  // 教材相关API
  textbookList = '/word/textbook/list',
  // 教材明细相关API
  detailTree = '/word/textbook/detail/tree',
  detailList = '/word/textbook/detail/list',
  detailSave = '/word/textbook/detail/save',
  detailUpdate = '/word/textbook/detail/update',
  detailDelete = '/word/textbook/detail/delete',
  detailDetail = '/word/textbook/detail/detail',
}

/**
 * 获取教材列表
 */
export const getTextbookList = (params) => defHttp.get({ url: Api.textbookList, params });

/**
 * 获取教材树形结构
 */
export const getDetailTree = (params) => defHttp.get({ url: Api.detailTree, params });

/**
 * 获取教材明细列表
 */
export const getDetailList = (params) => defHttp.get({ url: Api.detailList, params });

/**
 * 保存教材明细
 */
export const saveDetail = (params) => defHttp.post({ url: Api.detailSave, params });

/**
 * 更新教材明细
 */
export const updateDetail = (params) => defHttp.put({ url: Api.detailUpdate, params });

/**
 * 删除教材明细
 */
export const deleteDetail = (params) => defHttp.delete({ url: Api.detailDelete, params });

/**
 * 获取教材明细详情
 */
export const getDetailById = (params) => defHttp.get({ url: Api.detailDetail, params }); 