<template>
  <div>
    <!-- 搜索条件 -->
    <div class="search-form" style="margin-bottom: 16px; padding: 16px; background: #fafafa;">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-input v-model:value="searchParams.wordName" placeholder="请输入单词名称" />
        </a-col>
        <a-col :span="6">
          <a-select v-model:value="searchParams.textbookId" placeholder="请选择教材" style="width: 100%">
            <a-select-option value="">全部教材</a-select-option>
            <!-- 这里需要加载教材列表 -->
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-select v-model:value="searchParams.difficultyLevel" placeholder="请选择难易程度" style="width: 100%">
            <a-select-option value="">全部难度</a-select-option>
            <a-select-option :value="1">简单</a-select-option>
            <a-select-option :value="2">中等</a-select-option>
            <a-select-option :value="3">困难</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-button type="primary" @click="handleSearch">查询</a-button>
          <a-button style="margin-left: 8px;" @click="handleReset">重置</a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 工具栏 -->
    <div class="table-toolbar" style="margin-bottom: 16px;">
      <a-button type="primary" @click="handleAdd">
        <template #icon><plus-outlined /></template>
        新增单词
      </a-button>
      <a-button type="primary" @click="handleImportExcel" style="margin-left: 8px;">
        <template #icon><upload-outlined /></template>
        Excel导入
      </a-button>
      <a-button type="primary" @click="handleImportText" style="margin-left: 8px;">
        <template #icon><file-text-outlined /></template>
        文本导入
      </a-button>
      <a-button @click="handleExport" style="margin-left: 8px;">
        <template #icon><download-outlined /></template>
        导出
      </a-button>
    </div>

    <!-- 数据表格 -->
    <a-table 
      :columns="columns" 
      :data-source="dataSource" 
      :pagination="pagination"
      :loading="loading"
      row-key="id"
      @change="handleTableChange"
    >
      <template #action="{ record }">
        <a-button type="link" size="small" @click="handleEdit(record)">编辑</a-button>
        <a-button type="link" size="small" danger @click="handleDelete(record)">删除</a-button>
      </template>
    </a-table>

    <!-- 新增/编辑模态框 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="isEdit ? '编辑单词' : '新增单词'"
      @ok="handleSubmit"
      @cancel="handleCancel"
      width="800px"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="单词名称" name="wordName" :rules="[{ required: true, message: '请输入单词名称' }]">
          <a-input v-model:value="formData.wordName" placeholder="请输入单词名称" />
        </a-form-item>
        <a-form-item label="音标" name="phonetic">
          <a-input v-model:value="formData.phonetic" placeholder="请输入音标" />
        </a-form-item>
        <a-form-item label="中文译义" name="translation" :rules="[{ required: true, message: '请输入中文译义' }]">
          <a-input v-model:value="formData.translation" placeholder="请输入中文译义" />
        </a-form-item>
        <a-form-item label="单词分类" name="wordCategory">
          <a-select v-model:value="formData.wordCategory" placeholder="请选择单词分类">
            <a-select-option value="noun">名词</a-select-option>
            <a-select-option value="verb">动词</a-select-option>
            <a-select-option value="adj">形容词</a-select-option>
            <a-select-option value="adv">副词</a-select-option>
            <a-select-option value="prep">介词</a-select-option>
            <a-select-option value="conj">连词</a-select-option>
            <a-select-option value="int">感叹词</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="难易程度" name="difficultyLevel">
          <a-select v-model:value="formData.difficultyLevel" placeholder="请选择难易程度">
            <a-select-option :value="1">简单</a-select-option>
            <a-select-option :value="2">中等</a-select-option>
            <a-select-option :value="3">困难</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="例句" name="exampleSentence">
          <a-textarea v-model:value="formData.exampleSentence" placeholder="请输入例句" :rows="2" />
        </a-form-item>
        <a-form-item label="例句翻译" name="exampleTranslation">
          <a-textarea v-model:value="formData.exampleTranslation" placeholder="请输入例句翻译" :rows="2" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { 
  PlusOutlined, 
  UploadOutlined, 
  FileTextOutlined, 
  DownloadOutlined 
} from '@ant-design/icons-vue'

// 表格列定义
const columns = [
  {
    title: '单词名称',
    dataIndex: 'wordName',
    key: 'wordName',
    width: 150,
  },
  {
    title: '音标',
    dataIndex: 'phonetic',
    key: 'phonetic',
    width: 200,
  },
  {
    title: '中文译义',
    dataIndex: 'translation',
    key: 'translation',
    width: 200,
  },
  {
    title: '单词分类',
    dataIndex: 'wordCategory',
    key: 'wordCategory',
    width: 100,
  },
  {
    title: '难易程度',
    dataIndex: 'difficultyLevel',
    key: 'difficultyLevel',
    width: 100,
    customRender: ({ text }) => {
      const levelMap = { 1: '简单', 2: '中等', 3: '困难' }
      return levelMap[text] || ''
    }
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180,
  },
  {
    title: '操作',
    key: 'action',
    slots: { customRender: 'action' },
    width: 150,
  },
]

// 响应式数据
const searchParams = reactive({
  wordName: '',
  textbookId: '',
  difficultyLevel: '',
})

const dataSource = ref([])
const loading = ref(false)
const modalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条`,
})

const formData = reactive({
  id: '',
  wordName: '',
  phonetic: '',
  translation: '',
  wordCategory: '',
  difficultyLevel: 1,
  exampleSentence: '',
  exampleTranslation: '',
})

// 初始化模拟数据
const mockData = [
  {
    id: '1',
    wordName: 'hello',
    phonetic: '/həˈləʊ/',
    translation: '你好',
    wordCategory: 'int',
    difficultyLevel: 1,
    createTime: '2025-01-15 10:00:00',
  },
  {
    id: '2',
    wordName: 'apple',
    phonetic: '/ˈæpl/',
    translation: '苹果',
    wordCategory: 'noun',
    difficultyLevel: 1,
    createTime: '2025-01-15 10:01:00',
  },
  {
    id: '3',
    wordName: 'beautiful',
    phonetic: '/ˈbjuːtɪfl/',
    translation: '美丽的',
    wordCategory: 'adj',
    difficultyLevel: 2,
    createTime: '2025-01-15 10:02:00',
  },
]

// 方法定义
const loadData = () => {
  loading.value = true
  // 模拟API调用
  setTimeout(() => {
    dataSource.value = mockData
    pagination.total = mockData.length
    loading.value = false
  }, 500)
}

const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    searchParams[key] = ''
  })
  loadData()
}

const handleAdd = () => {
  isEdit.value = false
  modalVisible.value = true
  resetForm()
}

const handleEdit = (record) => {
  isEdit.value = true
  modalVisible.value = true
  Object.keys(formData).forEach(key => {
    formData[key] = record[key] || ''
  })
}

const handleDelete = (record) => {
  message.success(`删除单词: ${record.wordName}`)
  loadData()
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    const action = isEdit.value ? '更新' : '新增'
    message.success(`${action}单词成功: ${formData.wordName}`)
    modalVisible.value = false
    loadData()
  } catch (error) {
    console.log('表单验证失败:', error)
  }
}

const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

const resetForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = key === 'difficultyLevel' ? 1 : ''
  })
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

const handleImportExcel = () => {
  message.info('Excel导入功能开发中...')
}

const handleImportText = () => {
  message.info('文本导入功能开发中...')
}

const handleExport = () => {
  message.info('导出功能开发中...')
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.search-form {
  border-radius: 6px;
}

.table-toolbar {
  display: flex;
  align-items: center;
}

:deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
}
</style> 